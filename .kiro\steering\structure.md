# Project Structure

## File Organization
```
/
├── index.html          # Main application file (contains HTML, CSS, and JS)
├── assets/             # Static assets directory
│   └── icons/          # Application icons
│       ├── favicon.png # Primary favicon
│       └── favicon2.png# Alternative favicon
└── .kiro/              # Kiro configuration
    └── steering/       # AI assistant guidance documents
```

## Architecture Patterns

### Single-File Application
- All code consolidated in `index.html` for simplicity
- Embedded CSS in `<style>` tags within `<head>`
- Embedded JavaScript at the end of `<body>`
- No external dependencies or separate files

### Page-Based Navigation
- **Page 1**: Single-task focus mode (`#page1`)
- **Page 2**: Multi-column dashboard (`#page2`) 
- **Page 3**: Future expansion placeholder
- CSS classes control page visibility with smooth transitions

### Component Structure
- **Widgets**: Self-contained UI components with settings panels
- **Modals**: Overlay dialogs for complex interactions
- **Columns**: Vertical layout sections in dashboard view

### Data Management
- localStorage keys follow pattern: `startupTodo_*`
- JSON serialization for complex data structures
- Automatic data persistence on user actions

### CSS Organization
- General styles (reset, typography, buttons)
- Page-specific styles (page1, page2, etc.)
- Component styles (widgets, modals, forms)
- Responsive design with flexbox/grid

### JavaScript Patterns
- Event-driven architecture
- DOM manipulation with vanilla JS
- Modular functions for specific features
- State management through localStorage