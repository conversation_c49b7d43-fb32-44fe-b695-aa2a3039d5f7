# Technology Stack

## Core Technologies
- **Frontend**: Vanilla HTML5, CSS3, and JavaScript (ES6+)
- **Architecture**: Single-page application with client-side routing
- **Storage**: Browser localStorage for data persistence
- **Language**: Chinese (zh-CN) with UTF-8 encoding

## Build System
This is a static web application that requires no build process:
- Direct file serving from any web server
- No compilation or bundling required
- Assets served directly from `/assets/` folder

## Common Commands
```bash
# Serve locally (any static server)
python -m http.server 8000
# or
npx serve .
# or
php -S localhost:8000

# Open in browser
start index.html  # Windows
open index.html   # macOS
```

## Key Libraries & Frameworks
- **No external dependencies** - Pure vanilla JavaScript
- **Font Stack**: System fonts (-apple-system, BlinkMacSystemFont, Segoe UI, Roboto)
- **Icons**: Unicode characters and custom favicon

## Browser Compatibility
- Modern browsers with ES6+ support
- localStorage API required
- CSS Grid and Flexbox support needed

## Development Approach
- Progressive enhancement
- Mobile-first responsive design
- Accessibility considerations built-in
- Clean separation of concerns (HTML structure, CSS styling, JS behavior)